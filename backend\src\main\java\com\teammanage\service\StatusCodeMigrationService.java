package com.teammanage.service;

import java.time.LocalDateTime;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.teammanage.constants.SubscriptionStatusConstants;
import com.teammanage.entity.AccountSubscription;
import com.teammanage.entity.TeamInvitation;
import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.mapper.AccountSubscriptionMapper;
import com.teammanage.mapper.TeamInvitationMapper;
import com.teammanage.mapper.TeamMemberMapper;
import com.teammanage.util.StatusCodeConverter;

/**
 * 状态码迁移服务
 * 
 * 提供使用状态码的示例方法，展示如何在业务逻辑中使用状态标识符
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class StatusCodeMigrationService {
    
    private static final Logger log = LoggerFactory.getLogger(StatusCodeMigrationService.class);
    
    @Autowired
    private TeamMemberMapper teamMemberMapper;
    
    @Autowired
    private TeamInvitationMapper teamInvitationMapper;
    
    @Autowired
    private AccountSubscriptionMapper accountSubscriptionMapper;
    
    /**
     * 使用状态码创建团队邀请的示例方法
     */
    @Transactional
    public TeamInvitation createInvitationWithStatusCode(Long teamId, Long inviterId, String email, String message) {
        TeamInvitation invitation = new TeamInvitation();
        invitation.setTeamId(teamId);
        invitation.setInviterId(inviterId);
        invitation.setInviteeEmail(email);
        invitation.setMessage(message);
        invitation.setInvitedAt(LocalDateTime.now());
        invitation.setExpiresAt(LocalDateTime.now().plusDays(3));
        
        // 设置状态
        invitation.setStatus(TeamInvitation.InvitationStatus.PENDING);

        teamInvitationMapper.insert(invitation);

        log.info("创建邀请成功: invitationId={}, status={}",
                invitation.getId(), invitation.getStatus());
        
        return invitation;
    }
    
    /**
     * 响应邀请的示例方法
     */
    @Transactional
    public void respondToInvitation(Long invitationId, Long userId, boolean accept) {
        TeamInvitation invitation = teamInvitationMapper.selectById(invitationId);
        if (invitation == null) {
            throw new RuntimeException("邀请不存在");
        }

        // 检查是否可以响应
        if (!invitation.canBeResponded()) {
            throw new RuntimeException("邀请无法响应");
        }

        // 设置新状态
        TeamInvitation.InvitationStatus newStatus = accept ?
            TeamInvitation.InvitationStatus.ACCEPTED :
            TeamInvitation.InvitationStatus.REJECTED;

        invitation.setStatus(newStatus);
        invitation.setInviteeId(userId);
        invitation.setRespondedAt(LocalDateTime.now());

        teamInvitationMapper.updateById(invitation);

        log.info("响应邀请: invitationId={}, newStatus={}, accept={}",
                invitationId, newStatus, accept);
    }
    
    /**
     * 创建团队成员的示例方法
     */
    @Transactional
    public TeamMember createTeamMember(Long teamId, Long accountId, boolean isCreator) {
        TeamMember member = new TeamMember();
        member.setTeamId(teamId);
        member.setAccountId(accountId);
        member.setAssignedAt(LocalDateTime.now());
        member.setIsActive(true);
        member.setIsDeleted(false);

        // 设置角色
        TeamRole role = isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER;
        member.setRole(role);

        teamMemberMapper.insert(member);

        log.info("创建团队成员: memberId={}, role={}",
                member.getId(), member.getRole());

        return member;
    }
    
    /**
     * 创建订阅的示例方法
     */
    @Transactional
    public AccountSubscription createSubscription(Long accountId, Long planId) {
        AccountSubscription subscription = new AccountSubscription();
        subscription.setAccountId(accountId);
        subscription.setSubscriptionPlanId(planId);
        subscription.setStartDate(java.time.LocalDate.now());
        subscription.setEndDate(java.time.LocalDate.now().plusMonths(1));

        // 设置状态
        subscription.setStatus(SubscriptionStatusConstants.ACTIVE_CODE);

        accountSubscriptionMapper.insert(subscription);

        log.info("创建订阅: subscriptionId={}, status={}",
                subscription.getId(), subscription.getStatus());

        return subscription;
    }
    
    /**
     * 查询待处理邀请的示例方法
     */
    public List<TeamInvitation> findPendingInvitations(String email) {
        // 这里需要在Mapper中添加相应的查询方法
        // 示例：SELECT * FROM team_invitation WHERE invitee_email = ? AND status = 'PENDING'

        log.info("查询待处理邀请: email={}, status={}",
                email, TeamInvitation.InvitationStatus.PENDING);

        // 返回空列表作为示例
        return List.of();
    }

    /**
     * 检查权限的示例方法
     */
    public boolean hasManagePermission(TeamRole role) {
        // 进行权限检查
        return role != null && role == TeamRole.TEAM_CREATOR;
    }
    
    /**
     * 状态码转换示例方法
     */
    public void demonstrateStatusCodeConversion() {
        // 枚举转状态码
        Integer creatorCode = StatusCodeConverter.TeamRoleConverter.toCode(TeamRole.TEAM_CREATOR);
        Integer pendingCode = StatusCodeConverter.InvitationStatusConverter.toCode(TeamInvitation.InvitationStatus.PENDING);
        Integer activeCode = StatusCodeConverter.SubscriptionStatusConverter.toCode(AccountSubscription.SubscriptionStatus.ACTIVE);
        
        log.info("枚举转状态码示例: creatorCode={}, pendingCode={}, activeCode={}", 
                creatorCode, pendingCode, activeCode);
        
        // 状态码转枚举
        TeamRole role = StatusCodeConverter.TeamRoleConverter.fromCode(100);
        TeamInvitation.InvitationStatus status = StatusCodeConverter.InvitationStatusConverter.fromCode(1);
        AccountSubscription.SubscriptionStatus subStatus = StatusCodeConverter.SubscriptionStatusConverter.fromCode(1);
        
        log.info("状态码转枚举示例: role={}, status={}, subStatus={}", 
                role, status, subStatus);
    }
}
